// Comprehensive test suite for Limiter class with Fixed Window Rate Limiting
import { Limiter } from '../app/utils/limiter'

// Test suite implementation
interface TestResult {
    name: string
    passed: boolean
    error?: string
    duration?: number
}

const results: TestResult[] = []

function assert(condition: boolean, message: string) {
    if (!condition) {
        throw new Error(message)
    }
}

async function runTest(name: string, testFn: () => Promise<void>) {
    const start = Date.now()

    try {
        await testFn()

        results.push({
            name,
            passed: true,
            duration: Date.now() - start,
        })

        console.warn(`✅ ${name} (${Date.now() - start}ms)`)
    } catch (error) {
        results.push({
            name,
            passed: false,
            error: error instanceof Error ? error.message : String(error),
            duration: Date.now() - start,
        })

        console.error(`❌ ${name}: ${error instanceof Error ? error.message : String(error)}`)
    }
}

async function sleep(ms: number) {
    return new Promise((resolve) => setTimeout(resolve, ms))
}

// Basic test to satisfy linter
function _test() {
    return true
}

_test()

async function testNormalFlow() {
    const limiter = new Limiter({ maxRequestsPerSecond: 5 })
    const results: number[] = []
    const start = Date.now()

    for (let i = 0; i < 3; i++) {
        await limiter.schedule(() => {
            results.push(Date.now() - start)

            return i
        })
    }

    assert(results.length === 3, 'Should process all 3 requests')
    assert(results.every((time) => time < 100), 'All requests should be processed immediately (under 100ms)')
}

async function testRateLimiting() {
    const limiter = new Limiter({ maxRequestsPerSecond: 5 })
    const results: number[] = []
    const start = Date.now()

    const promises: Array<Promise<number>> = []

    for (let i = 0; i < 7; i++) {
        promises.push(limiter.schedule(() => {
            results.push(Date.now() - start)

            return i
        }))
    }

    await Promise.all(promises)

    assert(results.length === 7, 'Should process all 7 requests')

    const immediateRequests = results.filter((time) => time < 100)
    const delayedRequests = results.filter((time) => time >= 900)

    assert(immediateRequests.length === 5, 'First 5 requests should be immediate')
    assert(delayedRequests.length === 2, 'Last 2 requests should be delayed to next second')
}

async function testConcurrentRequests() {
    const limiter = new Limiter({ maxRequestsPerSecond: 3 })
    const results: number[] = []
    const start = Date.now()

    const promises = Array.from({ length: 6 }, (_, i) => limiter.schedule(async () => {
        results.push(Date.now() - start)
        await sleep(50)

        return i
    }))

    await Promise.all(promises)

    assert(results.length === 6, 'Should process all 6 requests')

    const immediateRequests = results.filter((time) => time < 200)
    const delayedRequests = results.filter((time) => time >= 900)

    assert(immediateRequests.length === 3, 'First 3 requests should be immediate')
    assert(delayedRequests.length === 3, 'Last 3 requests should be delayed')
}

async function testWeightAndPriority() {
    const limiter = new Limiter({ maxRequestsPerSecond: 2 })
    const results: Array<{ value: number, time: number }> = []
    const start = Date.now()

    const promises = [
        limiter.schedule(() => {
            const time = Date.now() - start
            results.push({ value: 1, time })

            return 1
        }, { weight: 1, priority: 5 }),

        limiter.schedule(() => {
            const time = Date.now() - start
            results.push({ value: 2, time })

            return 2
        }, { weight: 1, priority: 1 }),

        limiter.schedule(() => {
            const time = Date.now() - start
            results.push({ value: 3, time })

            return 3
        }, { weight: 1, priority: 9 }),
    ]

    await Promise.all(promises)

    assert(results.length === 3, 'Should process all 3 requests')

    const immediateRequests = results.filter((r) => r.time < 100)
    assert(immediateRequests.length === 2, 'First 2 requests should be immediate')
}

async function testEventEmission() {
    const limiter = new Limiter({ maxRequestsPerSecond: 2 })
    let limitEventFired = false
    let limitEventData: any = null

    limiter.on('limit', (until, metadata) => {
        limitEventFired = true
        limitEventData = { until, metadata }
    })

    const promises = Array.from({ length: 4 }, (_, i) => limiter.schedule(() => i))

    await Promise.all(promises)

    assert(limitEventFired, 'Limit event should be fired when rate limit is hit')
    assert(limitEventData !== null, 'Limit event should contain data')
    assert(limitEventData.until instanceof Date, 'Until should be a Date')
    assert(typeof limitEventData.metadata.currentWindow === 'number', 'Metadata should contain currentWindow')
}

async function testBoundaryConditions() {
    const limiter = new Limiter({ maxRequestsPerSecond: 1 })
    const results: number[] = []
    const start = Date.now()

    await limiter.schedule(() => {
        results.push(Date.now() - start)

        return 1
    })

    await sleep(1100)

    await limiter.schedule(() => {
        results.push(Date.now() - start)

        return 2
    })

    assert(results.length === 2, 'Should process both requests')
    assert(results[0] < 100, 'First request should be immediate')
    assert(results[1] > 1000, 'Second request should be in next window')
}

async function testErrorHandling() {
    const limiter = new Limiter({ maxRequestsPerSecond: 5 })
    let errorCaught = false

    try {
        await limiter.schedule(() => {
            throw new Error('Test error')
        })
    } catch (error) {
        errorCaught = true
        assert(error instanceof Error, 'Should catch the thrown error')
        assert((error as Error).message === 'Test error', 'Should preserve error message')
    }

    assert(errorCaught, 'Should catch and propagate errors from scheduled functions')
}

async function main() {
    console.warn('🧪 Running Limiter Tests...\n')

    await runTest('Normal Flow - Under Limit', testNormalFlow)
    await runTest('Rate Limiting - Over Limit', testRateLimiting)
    await runTest('Concurrent Requests', testConcurrentRequests)
    await runTest('Weight and Priority', testWeightAndPriority)
    await runTest('Event Emission', testEventEmission)
    await runTest('Boundary Conditions', testBoundaryConditions)
    await runTest('Error Handling', testErrorHandling)

    console.warn('\n📊 Test Results:')
    console.warn(`✅ Passed: ${results.filter((r) => r.passed).length}`)
    console.warn(`❌ Failed: ${results.filter((r) => !r.passed).length}`)
    console.warn(`⏱️  Total Duration: ${results.reduce((sum, r) => sum + (r.duration ?? 0), 0)}ms`)

    if (results.some((r) => !r.passed)) {
        console.warn('\n❌ Failed Tests:')

        results.filter((r) => !r.passed).forEach((r) => {
            console.warn(`  - ${r.name}: ${r.error}`)
        })

        process.exit(1)
    } else {
        console.warn('\n🎉 All tests passed!')
        process.exit(0)
    }
}

main().catch((error) => {
    console.error('Test runner error:', error)
    process.exit(1)
})
