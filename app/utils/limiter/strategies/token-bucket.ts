import type { Emitter } from '@kdt310722/utils/event'
import type { Awaitable } from '@kdt310722/utils/promise'
import Bottleneck from 'bottleneck'
import { type LimiterScheduleOptions, Strategy, type StrategyEvents } from './strategy'

export interface TokenBucketStrategyConfig {
    capacity: number
    refillRate: number
}

export class TokenBucketStrategy extends Strategy<TokenBucketStrategyConfig> {
    protected readonly bottleneck: Bottleneck

    public constructor(config: TokenBucketStrategyConfig, emitter: Emitter<StrategyEvents>) {
        super(config, emitter)

        this.bottleneck = this.createBottleneck(config)
    }

    public async schedule<T>(fn: () => Awaitable<T>, { weight = 1, priority = 5 }: LimiterScheduleOptions) {
        // Implement token bucket logic
    }

    protected createBottleneck(config: TokenBucketStrategyConfig) {
        const bottleneck = new Bottleneck({
            // Pass config to bottleneck
        })

        // Subscribe to events

        return bottleneck
    }
}
