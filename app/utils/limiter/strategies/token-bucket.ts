import type { Emitter } from '@kdt310722/utils/event'
import type { Awaitable } from '@kdt310722/utils/promise'
import Bottleneck from 'bottleneck'
import { type LimiterScheduleOptions, Strategy, type StrategyEvents } from './strategy'

export interface TokenBucketStrategyConfig {
    capacity: number
    refillRate: number
}

export class TokenBucketStrategy extends Strategy<TokenBucketStrategyConfig> {
    protected readonly bottleneck: Bottleneck

    public constructor(config: TokenBucketStrategyConfig, emitter: Emitter<StrategyEvents>) {
        super(config, emitter)

        this.bottleneck = this.createBottleneck(config)
    }

    public async schedule<T>(fn: () => Awaitable<T>, options: LimiterScheduleOptions = {}): Promise<T> {
        const { weight = 1, priority = 5 } = options

        return this.bottleneck.schedule({ weight, priority }, async () => {
            return await fn()
        })
    }

    protected createBottleneck(config: TokenBucketStrategyConfig) {
        const bottleneck = new Bottleneck({
            reservoir: config.capacity,
            reservoirRefreshAmount: config.refillRate,
            reservoirRefreshInterval: 1000,
            maxConcurrent: null,
            minTime: 0,
            id: `token-bucket-${config.capacity}cap-${config.refillRate}rate`,
        })

        bottleneck.on('depleted', () => {
            this.emitter.emit('limit', new Date(Date.now() + 1000), {
                capacity: config.capacity,
                refillRate: config.refillRate,
            })
        })

        bottleneck.on('error', (error) => {
            console.error('TokenBucketStrategy bottleneck error:', error)
        })

        return bottleneck
    }
}
