import type { Emitter } from '@kdt310722/utils/event'
import type { Awaitable } from '@kdt310722/utils/promise'
import Bottleneck from 'bottleneck'
import { type LimiterScheduleOptions, Strategy, type StrategyEvents } from './strategy'

export interface FixedWindowStrategyConfig {
    maxRequestsPerSecond: number
}

export class FixedWindowStrategy extends Strategy<FixedWindowStrategyConfig> {
    protected readonly bottleneck: Bottleneck

    public constructor(config: FixedWindowStrategyConfig, emitter: Emitter<StrategyEvents>) {
        super(config, emitter)

        this.bottleneck = this.createBottleneck(config)
    }

    public async schedule<T>(fn: () => Awaitable<T>, { weight = 1, priority = 5 }: LimiterScheduleOptions = {}): Promise<T> {
        return this.bottleneck.schedule({ weight, priority }, async () => fn())
    }

    protected createBottleneck(config: FixedWindowStrategyConfig) {
        const bottleneck = new Bottleneck({
            reservoir: config.maxRequestsPerSecond,
            reservoirRefreshInterval: 1000,
            reservoirRefreshAmount: config.maxRequestsPerSecond,
            maxConcurrent: null,
            minTime: 0,
            id: `fixed-window-${config.maxRequestsPerSecond}rps`,
        })

        bottleneck.on('depleted', () => {
            const currentWindow = Math.floor(Date.now() / 1000)
            const nextWindowAt = new Date((currentWindow + 1) * 1000)

            this.emitter.emit('limit', nextWindowAt, {})
        })

        bottleneck.on('error', (error) => {
            console.error('FixedWindowStrategy bottleneck error:', error)
        })

        return bottleneck
    }
}
