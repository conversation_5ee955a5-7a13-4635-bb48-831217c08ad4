import type { Awaitable } from '@kdt310722/utils/promise'
import type { LimiterScheduleOptions, Strategy, StrategyEvents } from './strategies/strategy'
import { Emitter } from '@kdt310722/utils/event'
import { FixedWindowStrategy } from './strategies/fixed-window'

export interface LimiterConfig {
    maxRequestsPerSecond: number
}

export class Limiter extends Emitter<StrategyEvents> {
    protected readonly strategy: Strategy

    public constructor(config: LimiterConfig) {
        super()

        this.strategy = this.getStrategy(config)
        this.setupEventForwarding()
    }

    public async schedule<T>(fn: () => Awaitable<T>, options?: LimiterScheduleOptions) {
        return this.strategy.schedule(fn, options)
    }

    protected getStrategy(config: LimiterConfig): Strategy {
        if (config.maxRequestsPerSecond) {
            return new FixedWindowStrategy({
                maxRequestsPerSecond: config.maxRequestsPerSecond,
            }, this)
        }

        throw new Error('Invalid limiter configuration: no valid strategy found')
    }

    protected setupEventForwarding() {
        // Events are already forwarded since we pass 'this' as emitter to strategy
    }
}
