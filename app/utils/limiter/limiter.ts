import type { Awaitable } from '@kdt310722/utils/promise'
import type { LimiterScheduleOptions, Strategy, StrategyEvents } from './strategies/strategy'
import { Emitter } from '@kdt310722/utils/event'

export interface LimiterConfig {
    maxRequestsPerSecond: number

    // More configs
}

export class Limiter extends Emitter<StrategyEvents> {
    protected readonly strategy: Strategy

    public constructor(config: LimiterConfig) {
        super()

        this.strategy = this.getStrategy(config)
    }

    public async schedule<T>(fn: () => Awaitable<T>, options?: LimiterScheduleOptions) {
        return this.strategy.schedule(fn, options)
    }

    protected getStrategy(config: LimiterConfig): Strategy {
        // Automatically select the best strategy based on the config
    }
}
