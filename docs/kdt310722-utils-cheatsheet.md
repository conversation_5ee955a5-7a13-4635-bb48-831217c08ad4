# @kdt310722/utils Cheatsheet

## Table of Contents

- [Overview](#overview)
- [Installation](#installation)
- [Module Structure](#module-structure)
- [API Reference](#api-reference)
- [Usage Examples](#usage-examples)
- [Best Practices](#best-practices)
- [Dependencies](#dependencies)

## Overview

`@kdt310722/utils` is a comprehensive collection of utility functions for JavaScript/TypeScript applications. It provides well-tested, type-safe utilities organized into logical modules for common programming tasks.

**Key Features:**

- Modular design with tree-shaking support
- Full TypeScript support with strict typing
- Comprehensive test coverage
- Minimal external dependencies
- Performance-optimized implementations
- Consistent API design across modules

## Installation

```bash
npm install @kdt310722/utils
# or
pnpm add @kdt310722/utils
# or
yarn add @kdt310722/utils
```

**Requirements:**

- Node.js >= 22.15.0

## Module Structure

The library is organized into the following modules:

- **array** - Array manipulation, chunking, iteration, and math operations
- **buffer** - Buffer operations and conversions
- **common** - Common utilities, type guards, and assertions
- **error** - Error handling and custom error classes
- **event** - Event emitter and event handling
- **function** - Function utilities and higher-order functions
- **json** - JSON parsing with custom serializers/deserializers
- **node** - Node.js specific utilities (fs, env, process)
- **number** - Number formatting, manipulation, and BigInt support
- **object** - Object manipulation and utilities
- **promise** - Promise utilities, async helpers, and concurrency control
- **string** - String manipulation, formatting, and validation
- **time** - Time and date utilities

## API Reference

### Import Patterns

```typescript
// Import entire module namespaces
import { arr, buffer, common, error, event, func, json, node, num, obj, promise, str, time } from '@kdt310722/utils'

// Import specific utilities
import { unique, chunk, range, flatten } from '@kdt310722/utils/array'
import { isObject, pick, omit, flatten as flattenObject } from '@kdt310722/utils/object'
import { sleep, createDeferred, withRetry } from '@kdt310722/utils/promise'
import { capitalize, hasPrefix, stripSuffix } from '@kdt310722/utils/string'
```

### Array Module (`@kdt310722/utils/array`)

#### Core Array Functions

```typescript
// Type guards and validation
function isArray<T = any>(value: unknown): value is T[]
function isIterable<T = any>(value: unknown): value is Iterable<T>

// Array creation and conversion
function wrap<T>(array: T | T[]): T[]
function toArray<T>(value?: Nullable<Arrayable<T>>): T[]
function range(from: number, to: number, step?: number): number[]
function flatten<T>(array?: Nullable<Arrayable<T | T[]>>): T[]
function merge<T>(...arrays: Array<Nullable<Arrayable<T>>>): T[]

// Array manipulation
function unique<T>(array: T[]): T[]
function uniqueBy<T>(array: T[], equalFn: (a: T, b: T) => boolean): T[]
function intersection<T>(a: T[], b: T[]): T[]
function diff<T>(a: T[], b: T[]): T[]
function symmetricDiff<T>(a: T[], b: T[]): T[]

// Async operations
function mapAsync<T, R>(array: T[], fn: (value: T, index: number, array: T[]) => Promise<R>): Promise<R[]>
function findAsync<T>(array: T[], predicate: (value: T) => Promise<boolean>): Promise<T | undefined>
```

#### Element Operations

```typescript
function push<T>(array: T[], ...values: T[]): T[]
function insert<T>(array: T[], index: number, ...values: T[]): T[]
function remove<T>(array: T[], value: T): T[]
function removeAt<T>(array: T[], index: number): T[]
function move<T>(array: T[], from: number, to: number): T[]
function swap<T>(array: T[], a: number, b: number): T[]
function at<T>(array: T[], index: number): T | undefined
function last<T>(array: T[]): T | undefined
function shuffle<T>(input: T[]): T[]
function sample<T>(array: T[], quantity?: number): T[]
```

#### Chunking

```typescript
// Generator-based chunking for memory efficiency
function chunk<T>(arr: Iterable<T>, size: number): Iterable<T[]>
```

#### Async Iterators

```typescript
interface ChunkAsyncIteratorOptions<T> {
    filter?: (item: T) => boolean
    transform?: (item: T) => T
}

function chunkAsyncIterator<T>(
    iterator: AsyncIterable<T>,
    chunkSize: number,
    options?: ChunkAsyncIteratorOptions<T>
): AsyncIterable<T[]>

function mergeAsyncIterators<T>(...iterators: Array<AsyncIterable<T>>): AsyncIterable<T>
```

#### Math Operations

```typescript
function sum(array: number[]): number
function sumBigint(array: bigint[]): bigint
function avg(array: number[]): number
function avgBigint(array: bigint[]): bigint
```

#### LRU Set

```typescript
class LruSet<T = unknown> extends Set<T> {
    constructor(public readonly maxSize: number)
    add(value: T): this
    // Inherits all Set methods
}
```

#### Type Definitions

```typescript
type Arrayable<T> = T | T[] | Iterable<T>
type ElementOf<T> = T extends Array<infer E> ? E : never
type NonEmpty<T = any> = [T, ...T[]]
```

### Buffer Module (`@kdt310722/utils/buffer`)

#### Core Functions

```typescript
type BufferLike = Buffer | ArrayBuffer | string

function join(...buffers: Array<BufferLike | BufferLike[]>): string
function toString(buffer: BufferLike, encoding?: BufferEncoding): string
```

### Promise Module (`@kdt310722/utils/promise`)

#### Core Functions

```typescript
// Basic utilities
function sleep(ms: number): Promise<void>
function waitUntilNextSecond(): Promise<void>
function withTimeout<T>(promise: Promise<T>, ms: number, message?: Error | (() => Error) | string): Promise<T>
```

#### Deferred Promises

```typescript
interface DeferredPromise<T> extends Promise<T> {
    resolve: (value: T | PromiseLike<T>) => void
    reject: (reason?: unknown) => void
    isSettled: boolean
    isPending: boolean
    isResolved: boolean
    isRejected: boolean
}

interface CreateDeferredOptions<T> {
    onResolve?: (value: T | PromiseLike<T>) => void
    onReject?: (reason: unknown) => void
    onSettle?: () => void
    onError?: (error: unknown) => void
}

function createDeferred<T>(options?: CreateDeferredOptions<T>): DeferredPromise<T>
```

#### Concurrency Control

```typescript
interface PromiseLock {
    run: <T = void>(fn: () => Promise<T>) => Promise<T>
    wait: () => Promise<void>
    clear: () => void
    isWaiting: () => boolean
}

function createLock(): PromiseLock
```

#### Async Utilities

```typescript
function waterfall<T>(tasks: Array<(prev: T) => Promise<T>>, initial: T): Promise<T>
function pipe<T>(...fns: Array<(value: T) => Promise<T>>): (value: T) => Promise<T>
function tap<T>(value: T, fn: (value: T) => void | Promise<void>): Promise<T>
```

#### Retry Logic

```typescript
type RetryOptions = Exclude<Options, number[]> & {
    delay?: number
}

function withRetry<T extends Fn>(fn: T, maxAttempts?: number, delay?: number): Promise<ReturnType<T>>
function withRetry<T extends Fn>(fn: T, options?: RetryOptions): Promise<ReturnType<T>>
```

#### Polling

```typescript
interface PollOptions {
    interval?: number
    timeout?: number
    immediate?: boolean
}

function poll<T>(fn: () => Promise<T>, condition: (result: T) => boolean, options?: PollOptions): Promise<T>
```

#### Abortable Operations

```typescript
function makeAbortable<T>(promise: Promise<T>, signal?: AbortSignal): Promise<T>
```

#### Type Definitions

```typescript
type Awaitable<T> = T | PromiseLike<T>
```

### Error Module (`@kdt310722/utils/error`)

#### Base Error Class

```typescript
type ErrorCode = string | number | symbol

interface BaseErrorOptions extends ErrorOptions {
    name?: string
    code?: ErrorCode
    retryable?: boolean
}

abstract class BaseError extends Error {
    public readonly timestamp: Date
    public readonly code?: ErrorCode
    public readonly retryable?: boolean

    public constructor(message?: string, options?: BaseErrorOptions)
    protected withValue<T>(key: string, value?: T): this
    public toJSON(): Record<string, unknown>
    public toString(): string
}
```

#### Error Utilities

```typescript
function createAbortError(message?: string): DOMException
function isAbortError(error: unknown): error is DOMException
function formatCause(cause: unknown, visited: WeakSet<object>, isInCauseChain?: boolean): string
function buildCauseChain(cause: unknown, maxDepth?: number): string
function stringifyError(error: Error, options?: ErrorStringifyOptions): string

interface ErrorStringifyOptions {
    includeCode?: boolean
    includeCause?: boolean
    maxCauseDepth?: number
}
```

### Event Module (`@kdt310722/utils/event`)

#### Event Emitter

```typescript
type EventName = string | symbol
type EventListener = (...args: any[]) => void
type Events = Record<EventName, EventListener>

class Emitter<TEvents extends Events, Strict extends boolean = false> {
    on<N extends keyof TEvents>(name: N, listener: TEvents[N]): this
    once<N extends keyof TEvents>(name: N, listener: TEvents[N]): this
    addListener<N extends keyof TEvents>(name: N, listener: TEvents[N]): this
    off<N extends keyof TEvents>(name: N, listener: TEvents[N]): this
    removeListener<N extends keyof TEvents>(name: N, listener: TEvents[N]): this
    removeAllListeners<N extends keyof TEvents>(name?: N): this
    listeners<N extends keyof TEvents>(name: N): Array<TEvents[N]>
    emit<N extends keyof TEvents>(name: N, ...args: Args<TEvents[N]>): boolean
    listenerCount<N extends keyof TEvents>(name: N, listener?: TEvents[N]): number
}
```

### String Module (`@kdt310722/utils/string`)

#### Core Functions

```typescript
// Type guards
function isString(value: unknown): value is string

// Prefix/suffix operations
function hasPrefix(str: string, prefix: string): boolean
function hasSuffix(str: string, suffix: string): boolean
function ensurePrefix(str: string, prefix: string): string
function ensureSuffix(str: string, suffix: string): string
function stripPrefix(str: string, prefix: string): string
function stripSuffix(str: string, suffix: string): string

// Text manipulation
function capitalize(str: string): string
function chunk(str: string, size: number): string[]
function shorten(str: string, size: number): string
function truncate(str: string, size: number, omission?: string): string

// String comparison
function includes(str: string, search: string | string[], type?: 'any' | 'all'): boolean
function equals(str: string, ...others: string[]): boolean
function equalsIgnoreCase(str: string, ...others: string[]): boolean

// Generation
function random(length?: number, characters?: string): string
```

#### String Trimming

```typescript
function ltrim(str: string, characters?: string): string
function rtrim(str: string, characters?: string): string
function trim(str: string, characters?: string): string
function trimRepeated(input: string, target: string): string
```

#### Hex Utilities

```typescript
type Hex = `0x${string}`

function isHexString(value: unknown, length?: number): value is string
function isStrictHexString(value: unknown, length?: number): value is Hex
function stripHexPrefix(value: string): string
function ensureHexPrefix(value: string): Hex
function hexEquals(hex: string, ...others: string[]): boolean
```

#### URL Utilities

```typescript
function isValidUrl(url: string, protocols?: string[]): boolean
function isWebSocketUrl(url: string): boolean
function isHttpUrl(url: string): boolean
```

#### Regular Expression

```typescript
function escapeRegExp(input: string): string
```

### Time Module (`@kdt310722/utils/time`)

#### Core Functions

```typescript
function isDate(value: unknown): value is Date
function toTimestamp(date: Date): number
function timestamp(): number
function padZeroStart(num: number, length: number): string
function formatDate(date: Date, showMilliseconds?: boolean): string
```

### Common Module (`@kdt310722/utils/common`)

#### Type Guards

```typescript
function typeOf(v: unknown): string
function isNull(value: unknown): value is null
function isUndefined(value?: unknown): value is undefined
function isNullish(value?: unknown): value is null | undefined
function isBoolean(value: unknown): value is boolean
function notNull<T>(value: T | null): value is Exclude<T, null>
function notUndefined<T>(value: T | undefined): value is Exclude<T, undefined>
function notNullish<T>(value: Nullable<T>): value is NonNullable<T>
```

#### Utility Functions

```typescript
function isEmpty(value: any): boolean
function isTrueLike(value: unknown): boolean
function toString(value: unknown): string
```

#### Deep Equality

```typescript
function isDeepEqual(a: any, b: any): boolean
```

#### Assertions

```typescript
function assert(condition: boolean, message: string | Error): asserts condition
```

#### Type Definitions

```typescript
type Primitive = null | undefined | string | number | boolean | symbol | bigint
type Nullable<T> = T | null | undefined
type IsContainsType<T, U> = Extract<T, U> extends never ? false : true
type Constructor<T> = new (...args: any[]) => T
type MethodArgs<C, M extends keyof C> = C[M] extends Fn ? Args<C[M]> : never
type MethodReturn<C, M extends keyof C> = C[M] extends Fn ? ReturnType<C[M]> : never
type ExcludeNull<O> = { [K in keyof O]: Exclude<O[K], null> }
type ExcludeUndefined<O> = { [K in keyof O]: Exclude<O[K], undefined> }
type ExcludeNullish<O> = ExcludeNull<ExcludeUndefined<O>>
type Mutable<T> = { -readonly [P in keyof T]: T[P] }
type ClassMethod<T> = { [K in keyof T]: T[K] extends Fn ? K : never }[keyof T]
```

### Function Module (`@kdt310722/utils/function`)

#### Core Functions

```typescript
// Type guard
function isFunction<T extends Fn>(value: unknown): value is T

// Basic utilities
function noop(): void
function invoke<F extends Fn>(fn: F): ReturnType<F>
function batchInvoke(functions: Array<Nullable<Fn>>): void
function tap<T>(value: T, callback: (value: T) => void): T
function transform<T, R>(value: T, callback: (value: T) => R): R

// Error handling
function tryCatch<T>(fn: () => T, fallback: T, throwsIf?: (error: unknown) => boolean): T
function tryCatchAsync<T>(fn: () => Promise<T>, fallback: Awaitable<T>, throwsIf?: (error: unknown) => boolean): Promise<T>
```

#### Higher-Order Functions

```typescript
function once<T extends Fn>(fn: T): T
```

#### Function Composition

```typescript
function pipe<TInitial>(init: TInitial): TInitial
function pipe<TInitial, R1>(init: TInitial, init_r1: (init: TInitial) => R1): R1
// ... overloads for up to 10 functions
function pipe<TInitial>(init: TInitial, ...fns: CallableFunction[]): any
```

#### Type Definitions

```typescript
type Fn = (...args: any[]) => any
type Args<F extends Fn> = F extends (...args: infer A) => any ? A : never
```

### JSON Module (`@kdt310722/utils/json`)

#### Core Functions

```typescript
function parseJson<T = any>(json: string, deserializers?: JsonDeserializer | JsonDeserializer[]): T
function stringifyJson<T = any>(value: T, options?: StringifyJsonOptions): string

type JsonDeserializer = (key: string, value: any) => any
type JsonSerializer = (key: string, value: any) => any

interface StringifyJsonOptions {
    indent?: number
    serializers?: JsonSerializer | JsonSerializer[]
    json5?: boolean
}
```

#### Built-in Serializers/Deserializers

```typescript
// BigInt support
function bigIntSerializer(key: string, value: any): any
function bigintDeserializer(key: string, value: any): any

// Error support
function errorSerializer(key: string, value: any): any
function errorDeserializer(key: string, value: any): any
```

### Node Module (`@kdt310722/utils/node`)

#### Environment

```typescript
function isInMode(key: string): boolean
function isInDevelopment(): boolean
function isInProduction(): boolean
function isInStaging(): boolean
```

#### Fetch

```typescript
interface FetchOptions {
    timeout?: number
    retry?: (RetryOptions & { enabled?: boolean }) | boolean
}

function fetch(input: RequestInfo | URL, init?: RequestInit, options?: FetchOptions): Promise<Response>
```

#### File System

```typescript
// File operations
function exists(path: string): Promise<boolean>
function isFile(path: string): Promise<boolean>
function isDirectory(path: string): Promise<boolean>
function readFile(path: string, encoding?: BufferEncoding): Promise<string>
function writeFile(path: string, data: string | Buffer): Promise<void>
function mkdir(path: string, options?: { recursive?: boolean }): Promise<void>
```

#### Path

```typescript
function toString(path: PathLike): string
function dirname(importMeta: ImportMeta, ...path: PathLike[]): string
```

#### Process

```typescript
function gracefulExit(code?: number, timeout?: number): void
```

### Number Module (`@kdt310722/utils/number`)

#### Core Functions

```typescript
// Type guards
function isNumber(value: unknown): value is number
function isBigInt(value: unknown): value is bigint
function isNumberString(value: string): boolean
function isNumberish(input: unknown): input is Numberish
function isValidRange(start: number, end: number, inclusive?: boolean, min?: number, max?: number): boolean

// Conversion
function toBigInt(input: unknown): bigint
function toNumber(input: unknown): number

// Math utilities
function random(min: number, max: number): number
function minMax(input: number, min: number, max: number): number
function minMaxBigInt(input: bigint, min: bigint, max: bigint): bigint
```

#### Formatting

```typescript
interface FormatOptions extends Intl.NumberFormatOptions {
    locales?: string | string[]
    groupFractionLeadingZeros?: boolean
    exactFractionWhenZero?: boolean
    maximumFractionLeadingZeros?: number
}

function convertExponentialToString(num: Numberish): NumberString
function format(number: Numberish, options?: FormatOptions): string
function humanizeNumber(value: Numberish, options?: FormatOptions): string
function formatUsdCurrency(input: Numberish, options?: FormatOptions): string
function formatBytes(bytes: number, options?: FormatOptions): string
function formatPercent(value: number, options?: FormatOptions): string
function formatNanoseconds(nanoseconds: bigint): string
```

#### BigInt Math

```typescript
const BigIntMath: {
    abs(a: bigint): bigint
    max(a: bigint, b: bigint): bigint
    min(a: bigint, b: bigint): bigint
    pow(a: bigint, b: bigint): bigint
    sign(a: bigint): bigint
}
```

#### Type Definitions

```typescript
type NumberString = `${number}` | 'Infinity' | '-Infinity' | '+Infinity' | 'NaN'
type Numberish = NumberString | number | bigint
```

### Object Module (`@kdt310722/utils/object`)

#### Core Functions

```typescript
// Type guards
function isObject(value: unknown): value is AnyObject
function isPlainObject(value: unknown): value is AnyObject
function isEmptyObject(value: AnyObject): boolean
function isKeyOf<T extends AnyObject>(obj: T, name: PropertyKey): name is keyof T
function isKeysOf<T extends string>(data: AnyObject, keys: T[]): data is Record<T, unknown>
function hasOwnProperty<T extends AnyObject>(obj: T, name: PropertyKey): name is keyof T

// Object manipulation
function keys<T extends AnyObject>(obj: T): Array<keyof T>
function entries<O extends AnyObject>(obj: O): Array<[keyof O, O[keyof O]]>
function filter<O extends AnyObject>(obj: O, predicate: FilterPredicate<O, keyof O>): Partial<O>
function filterByValue<O extends AnyObject>(obj: O, predicate: (value: O[keyof O]) => boolean): Partial<O>
function pick<O extends AnyObject, K extends keyof O>(obj: O, ...keys: K[]): Pick<O, K>
function omit<O extends AnyObject, K extends keyof O>(object: O, ...keys: K[]): Omit<O, K>
function map<K extends PropertyKey, V, NK extends PropertyKey, NV>(
    obj: Record<K, V>,
    fn: (k: K, v: V, i: number) => Nullable<[NK, NV]>
): Record<NK, NV>
function sumBy<O extends AnyObject>(objects: O[], key: keyof PickByType<O, number>): number

// Path operations
function get<O extends AnyObject, P extends Paths<O, D>, D extends string = '.'>(obj: O, path: P, delimiter?: D): GetValue<O, P, D>
function set<O extends AnyObject, P extends string, V, D extends string = '.'>(target: O, path: P, value: V, delimiter?: D): SetValueByPath<O, P, V, D>
```

#### Object Flattening

```typescript
function flatten<O extends AnyObject, D extends string = '.'>(obj: O, delimiter?: D): Flatten<O, D>
function unflatten<R>(obj: AnyObject, delimiter?: string): R
```

#### Options Merging

```typescript
function resolveNestedOptions<T>(options: T | true): T
```

#### LRU Map

```typescript
class LruMap<K extends PropertyKey, T = unknown> extends Map<K, T> {
    constructor(public readonly maxSize: number)
    set(key: K, value: T): this
    // Inherits all Map methods
}
```

#### Type Definitions

```typescript
type AnyObject = Record<PropertyKey, any>
type PickByType<T, Value> = { [P in keyof T as T[P] extends Value | undefined ? P : never]: T[P] }
type FilterPredicate<O, K extends keyof O> = (key: K, value: O[K], index: number) => boolean
type Paths<T, D extends string = '.'> = T extends AnyObject ? { [K in keyof T]: `${Exclude<K, symbol>}${'' | `${D}${Paths<T[K]>}`}` }[keyof T] : never
type GetValue<T, P, D extends string = '.'> = P extends `${infer U}${D}${infer R}` ? (U extends keyof T ? GetValue<T[U], R, D> : never) : (P extends keyof T ? T[P] : never)
type SetValueByPath<O extends AnyObject, P extends string, V, D extends string = '.'> = P extends `${infer U}${D}${infer R}` ? U extends keyof O ? { [K in keyof O]: K extends U ? O[K] extends AnyObject ? SetValueByPath<O[K], R, V, D> : FromPath<R, V, D> : O[K] } : (O & Record<U, FromPath<R, V, D>>) : SetValue<O, P, V>
type Flatten<O extends AnyObject, D extends string = '.'> = { [P in FlattenKeys<O, D>]: GetValue<O, P, D> }
```

### Common Module (`@kdt310722/utils/common`)

#### Type Guards

```typescript
function typeOf(v: unknown): string
function isNull(value: unknown): value is null
function isUndefined(value?: unknown): value is undefined
function isNullish(value?: unknown): value is null | undefined
function isBoolean(value: unknown): value is boolean
function notNull<T>(value: T | null): value is Exclude<T, null>
function notUndefined<T>(value: T | undefined): value is Exclude<T, undefined>
function notNullish<T>(value: Nullable<T>): value is NonNullable<T>
```

#### Utility Functions

```typescript
function isEmpty(value: any): boolean
function isTrueLike(value: unknown): boolean
function toString(value: unknown): string
```

#### Deep Equality

```typescript
function isDeepEqual(a: any, b: any): boolean
```

#### Assertions

```typescript
function assert(condition: boolean, message: string | Error): asserts condition
```

#### Type Definitions

```typescript
type Primitive = null | undefined | string | number | boolean | symbol | bigint
type Nullable<T> = T | null | undefined
type IsContainsType<T, U> = Extract<T, U> extends never ? false : true
type Constructor<T> = new (...args: any[]) => T
type MethodArgs<C, M extends keyof C> = C[M] extends Fn ? Args<C[M]> : never
type MethodReturn<C, M extends keyof C> = C[M] extends Fn ? ReturnType<C[M]> : never
type ExcludeNull<O> = { [K in keyof O]: Exclude<O[K], null> }
type ExcludeUndefined<O> = { [K in keyof O]: Exclude<O[K], undefined> }
type ExcludeNullish<O> = ExcludeNull<ExcludeUndefined<O>>
type Mutable<T> = { -readonly [P in keyof T]: T[P] }
type ClassMethod<T> = { [K in keyof T]: T[K] extends Fn ? K : never }[keyof T]
```

### Function Module (`@kdt310722/utils/function`)

#### Core Functions

```typescript
// Type guard
function isFunction<T extends Fn>(value: unknown): value is T

// Basic utilities
function noop(): void
function invoke<F extends Fn>(fn: F): ReturnType<F>
function batchInvoke(functions: Array<Nullable<Fn>>): void
function tap<T>(value: T, callback: (value: T) => void): T
function transform<T, R>(value: T, callback: (value: T) => R): R

// Error handling
function tryCatch<T>(fn: () => T, fallback: T, throwsIf?: (error: unknown) => boolean): T
function tryCatchAsync<T>(fn: () => Promise<T>, fallback: Awaitable<T>, throwsIf?: (error: unknown) => boolean): Promise<T>
```

#### Higher-Order Functions

```typescript
function once<T extends Fn>(fn: T): T
function memoize<T extends Fn>(fn: T): T
```

#### Function Composition

```typescript
function pipe<T>(...fns: Array<(arg: T) => T>): (arg: T) => T
function compose<T>(...fns: Array<(arg: T) => T>): (arg: T) => T
```

#### Type Definitions

```typescript
type Fn = (...args: any[]) => any
type Args<T extends Fn> = T extends (...args: infer P) => any ? P : never
type Return<T extends Fn> = T extends (...args: any[]) => infer R ? R : never
```

### Number Module (`@kdt310722/utils/number`)

#### Core Functions

```typescript
// Type guards
function isNumber(value: unknown): value is number
function isBigInt(value: unknown): value is bigint
function isNumberString(value: string): boolean
function isNumberish(input: unknown): input is Numberish
function isValidRange(start: number, end: number, inclusive?: boolean, min?: number, max?: number): boolean

// Conversion
function toBigInt(input: unknown): bigint
function toNumber(input: unknown): number

// Math utilities
function random(min: number, max: number): number
function minMax(input: number, min: number, max: number): number
function minMaxBigInt(input: bigint, min: bigint, max: bigint): bigint
```

#### Formatting

```typescript
function formatBytes(bytes: number, decimals?: number): string
function formatNanoseconds(ns: bigint): string
```

#### BigInt Math

```typescript
const BigIntMath: {
    abs(x: bigint): bigint
    max(...values: bigint[]): bigint
    min(...values: bigint[]): bigint
    sign(x: bigint): bigint
}
```

#### Type Definitions

```typescript
type Numberish = number | bigint | string
```

### Error Module (`@kdt310722/utils/error`)

#### Base Error Class

```typescript
class BaseError extends Error {
    public readonly name: string
    public readonly code?: string
    public readonly cause?: Error
    public readonly context?: Record<string, unknown>

    public constructor(message: string, options?: {
        code?: string
        cause?: Error
        context?: Record<string, unknown>
    })

    public withValue<T>(key: string, value: T): BaseError
    public toJSON(): Record<string, unknown>
    public static from(error: unknown, message?: string): BaseError
}

class TimeoutError extends BaseError {}
class AbortError extends BaseError {}
class ValidationError extends BaseError {}
```

**Error Utilities**
```typescript
// Error handling and conversion
function isError(value: unknown): value is Error
function formatError(error: unknown): string
function serializeError(error: Error): Record<string, unknown>
function parseError(serialized: Record<string, unknown>): Error

// Error wrapping and context
function wrapError(error: unknown, message: string): BaseError
function addErrorContext(error: Error, context: Record<string, unknown>): Error
```

### JSON Module (`@kdt310722/utils/json`)

#### Core Functions

```typescript
function parseJson<T = any>(text: string, deserializers?: JsonDeserializer | JsonDeserializer[]): T

function stringifyJson(value: any, serializers?: JsonSerializer | JsonSerializer[]): string

type JsonDeserializer = (key: string, value: any) => any
type JsonSerializer = (key: string, value: any) => any
```

### Node Module (`@kdt310722/utils/node`)

#### File System

```typescript
function exists(path: string): Promise<boolean>
function isFile(path: string): Promise<boolean>
function isDirectory(path: string): Promise<boolean>
function isReadable(path: string): boolean
function isWritable(path: string): boolean

function readFile(path: string, encoding?: BufferEncoding): Promise<string>
function writeFile(path: string, data: string | Buffer): Promise<void>
function mkdir(path: string, options?: { recursive?: boolean }): Promise<void>
```

#### Process

```typescript
function gracefulExit(code?: number, timeout?: number): void
function getProcessInfo(): ProcessInfo
function isMainModule(): boolean
```

#### Environment

```typescript
function getEnv(key: string, defaultValue?: string): string | undefined
function setEnv(key: string, value: string): void
function isDevelopment(): boolean
function isProduction(): boolean
function isTest(): boolean
```

## Usage Examples

### Array Operations

```typescript
import { arr } from '@kdt310722/utils'
// or
import { chunk, sum, unique, shuffle, sample } from '@kdt310722/utils/array'

// Chunking data for batch processing
const items = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10]
for (const batch of chunk(items, 3)) {
    console.log('Processing batch:', batch) // [1, 2, 3], [4, 5, 6], [7, 8, 9], [10]
}

// Array math operations
const numbers = [1, 2, 3, 4, 5]
const total = sum(numbers) // 15
const average = avg(numbers) // 3

// Unique values and manipulation
const duplicates = [3, 1, 4, 1, 5, 9, 2, 6, 5]
const uniqueValues = unique(duplicates) // [3, 1, 4, 5, 9, 2, 6]
const shuffled = shuffle(uniqueValues) // Random order
const samples = sample(uniqueValues, 3) // 3 random elements

// Array set operations
const a = [1, 2, 3, 4]
const b = [3, 4, 5, 6]
const intersect = intersection(a, b) // [3, 4]
const difference = diff(a, b) // [1, 2]
const symDiff = symmetricDiff(a, b) // [1, 2, 5, 6]

// LRU Set for caching
const cache = new LruSet<string>(100)
cache.add('item1')
cache.add('item2')
console.log(cache.has('item1')) // true
console.log(cache.size) // 2

// Async operations
const asyncResults = await mapAsync([1, 2, 3], async (x) => x * 2) // [2, 4, 6]
const found = await findAsync([1, 2, 3], async (x) => x > 2) // 3
```

### Object Manipulation

```typescript
import { obj } from '@kdt310722/utils'
// or
import { pick, omit, flatten, unflatten, LruMap, isPlainObject, isEmpty } from '@kdt310722/utils/object'

// Object filtering
const user = { id: 1, name: 'Alice', email: '<EMAIL>', password: 'secret' }
const publicUser = omit(user, 'password') // { id: 1, name: 'Alice', email: '<EMAIL>' }
const credentials = pick(user, 'email', 'password') // { email: '<EMAIL>', password: 'secret' }

// Object flattening
const nested = {
    user: {
        profile: {
            name: 'Alice',
            settings: { theme: 'dark' }
        }
    }
}
const flat = flatten(nested)
// { 'user.profile.name': 'Alice', 'user.profile.settings.theme': 'dark' }

const restored = unflatten(flat)
// Back to original nested structure

// Path operations
const value = get(nested, 'user.profile.name') // 'Alice'
const updated = set({}, 'user.profile.name', 'Bob') // { user: { profile: { name: 'Bob' } } }

// LRU Map for caching
const cache = new LruMap<string, any>(1000)
cache.set('user:1', { name: 'Alice' })
cache.set('user:2', { name: 'Bob' })
const user1 = cache.get('user:1') // { name: 'Alice' }

// Type checking and utilities
console.log(isPlainObject({})) // true
console.log(isPlainObject([])) // false
console.log(isEmpty({})) // true
console.log(isEmpty({ a: 1 })) // false

// Object transformation
const mapped = map({ a: 1, b: 2 }, (k, v) => [k.toUpperCase(), v * 2]) // { A: 2, B: 4 }
const filtered = filter(user, (key, value) => typeof value === 'string')
```

### String Utilities

```typescript
import { str } from '@kdt310722/utils'
// or
import {
    ensurePrefix, stripPrefix, ensureSuffix, stripSuffix,
    isValidUrl, escapeRegExp, capitalize, random, trim
} from '@kdt310722/utils/string'

// Prefix/suffix operations
const withPrefix = ensurePrefix('world', 'hello-') // 'hello-world'
const cleaned = stripPrefix('hello-world', 'hello-') // 'world'
const withSuffix = ensureSuffix('file', '.txt') // 'file.txt'
const noExt = stripSuffix('file.txt', '.txt') // 'file'

// Hex utilities
const hexValue = ensureHexPrefix('1234') // '0x1234'
const withoutPrefix = stripHexPrefix('0x1234') // '1234'
console.log(isHexString('0x1234')) // true
console.log(hexEquals('0x1234', '1234')) // true

// URL validation
console.log(isValidUrl('https://example.com')) // true
console.log(isHttpUrl('https://example.com')) // true
console.log(isWebSocketUrl('ws://localhost:8080')) // true

// String formatting
const title = capitalize('hello world') // 'Hello world'
const randomStr = random(16) // Random 16-character string
const shortened = shorten('This is a very long string', 10) // 'This is a ...ong string'
const truncated = truncate('This is a long string', 10) // 'This is a ...'

// String trimming
const trimmed = trim('  hello  ') // 'hello'
const customTrim = trim('---hello---', '-') // 'hello'
const leftTrim = ltrim('  hello  ') // 'hello  '
const rightTrim = rtrim('  hello  ') // '  hello'

// String comparison
console.log(includes('hello world', ['hello', 'world'])) // true
console.log(equals('hello', 'hello', 'hello')) // true
console.log(equalsIgnoreCase('Hello', 'HELLO')) // true

// Regex escaping
const escaped = escapeRegExp('$100 (USD)') // '\\$100 \\(USD\\)'
```

### Promise Utilities

```typescript
import { promise } from '@kdt310722/utils'
// or
import { createDeferred, createLock, withRetry, withTimeout, sleep } from '@kdt310722/utils/promise'

// Deferred promises
const deferred = createDeferred<string>()
setTimeout(() => deferred.resolve('Done!'), 1000)
const result = await deferred.promise // 'Done!'

// Check deferred state
console.log(deferred.isSettled) // true
console.log(deferred.isResolved) // true
console.log(deferred.isRejected) // false

// Deferred with callbacks
const deferredWithCallbacks = createDeferred<string>({
    onResolve: (value) => console.log('Resolved:', value),
    onReject: (error) => console.log('Rejected:', error),
    onSettle: () => console.log('Settled')
})

// Promise lock for concurrency control
const lock = createLock()

// Run exclusive operations
await lock.run(async () => {
    console.log('Exclusive access')
    await sleep(1000)
})

// Wait for all operations to complete
await lock.wait()
console.log('All operations done')

// Retry with options
const unstableOperation = async () => {
    if (Math.random() < 0.7) throw new Error('Failed')
    return 'Success'
}

const result = await withRetry(unstableOperation, {
    retries: 5,
    delay: 1000
})

// Timeout wrapper
const slowOperation = () => new Promise((resolve) => setTimeout(resolve, 5000))
try {
    await withTimeout(slowOperation(), 3000, 'Operation timed out')
} catch (error) {
    console.log(error.message) // 'Operation timed out'
}

// Sleep utilities
await sleep(1000) // Wait 1 second
await waitUntilNextSecond() // Wait until next second boundary

// Async utilities
const values = [1, 2, 3]
const doubled = await waterfall(
    values.map(v => async (prev) => prev + v),
    0
) // 6

const tapped = await tap('value', async (v) => console.log('Processing:', v))
```

### Error Handling

```typescript
import { error } from '@kdt310722/utils'
// or
import { BaseError, createAbortError, isAbortError, stringifyError } from '@kdt310722/utils/error'

// Custom error with context
class ValidationError extends BaseError {
    public constructor(field: string, value: unknown) {
        super(`Invalid value for field: ${field}`, {
            code: 'VALIDATION_ERROR',
            retryable: false
        })
        this.withValue('field', field)
        this.withValue('value', value)
    }
}

// Create and use custom errors
const validationError = new ValidationError('email', 'invalid-email')
console.log(validationError.code) // 'VALIDATION_ERROR'
console.log(validationError.retryable) // false
console.log(validationError.timestamp) // Date object

// Error serialization
const errorJson = validationError.toJSON()
const errorString = validationError.toString()

// Abort errors
const abortError = createAbortError('Operation was cancelled')
console.log(isAbortError(abortError)) // true

// Error formatting and cause chains
const rootError = new Error('Database connection failed')
const wrappedError = new BaseError('Failed to save user', { cause: rootError })
const finalError = new BaseError('API request failed', { cause: wrappedError })

console.log(stringifyError(finalError, { includeCause: true, maxCauseDepth: 3 }))
// BaseError: API request failed
//   Caused by: BaseError: Failed to save user
//   Caused by: Error: Database connection failed

// Error context building
const contextError = new BaseError('Operation failed')
    .withValue('userId', 123)
    .withValue('operation', 'save')
    .withValue('timestamp', new Date())
```

### JSON Operations

```typescript
import { json } from '@kdt310722/utils'
// or
import { parseJson, stringifyJson, bigIntSerializer, errorSerializer } from '@kdt310722/utils/json'

// JSON parsing with built-in deserializers
const jsonString = '{"name": "Alice", "age": 25, "bigNumber": "123456789012345678901234567890n"}'
const parsed = parseJson(jsonString) // Automatically handles BigInt and Error objects

// Custom deserializers
const customDeserializer = (key: string, value: any) => {
    if (key === 'date' && typeof value === 'string') {
        return new Date(value)
    }
    return value
}

const data = parseJson('{"date": "2023-01-01T00:00:00.000Z"}', customDeserializer)

// JSON stringification with built-in serializers
const objectWithBigInt = {
    name: 'Alice',
    bigNumber: 123456789012345678901234567890n,
    error: new Error('Something went wrong')
}

const jsonStr = stringifyJson(objectWithBigInt) // Handles BigInt and Error serialization

// Pretty printing with JSON5 support
const formatted = stringifyJson(
    { a: 1, b: { c: 2 } },
    { indent: 2, json5: true }
)

// Custom serializers
const customSerializer = (key: string, value: any) => {
    if (value instanceof Date) {
        return value.toISOString()
    }
    return value
}

const withCustom = stringifyJson(
    { date: new Date(), name: 'Alice' },
    { serializers: customSerializer }
)
```

### Event Handling

```typescript
import { event } from '@kdt310722/utils'
// or
import { Emitter } from '@kdt310722/utils/event'

// Type-safe event emitter
interface Events {
    'user:created': (user: { id: number; name: string }) => void
    'user:updated': (data: { id: number; changes: Record<string, unknown> }) => void
    'error': (error: Error) => void
}

const emitter = new Emitter<Events>()

// Event listeners
emitter.on('user:created', (user) => {
    console.log('New user created:', user.name)
})

emitter.once('user:updated', (data) => {
    console.log('User updated:', data.id)
})

// Add multiple listeners
emitter.addListener('error', (error) => {
    console.error('Error occurred:', error.message)
})

// Emit events
emitter.emit('user:created', { id: 1, name: 'Alice' })
emitter.emit('user:updated', { id: 1, changes: { email: '<EMAIL>' } })

// Remove listeners
const errorHandler = (error: Error) => console.error(error)
emitter.on('error', errorHandler)
emitter.off('error', errorHandler) // Remove specific listener
emitter.removeAllListeners('error') // Remove all listeners for event
emitter.removeAllListeners() // Remove all listeners

// Check listeners
console.log(emitter.listenerCount('user:created')) // Number of listeners
console.log(emitter.listeners('user:created')) // Array of listeners
```

### Time and Date Utilities

```typescript
import { time } from '@kdt310722/utils'
// or
import { isDate, toTimestamp, timestamp, formatDate, padZeroStart } from '@kdt310722/utils/time'

// Date type checking
console.log(isDate(new Date())) // true
console.log(isDate('2023-01-01')) // false

// Timestamp utilities
const now = new Date()
const unixTimestamp = toTimestamp(now) // Unix timestamp in seconds
const currentTimestamp = timestamp() // Current Unix timestamp

// Date formatting
console.log(formatDate(now)) // '14:30:25 15/01/2024'
console.log(formatDate(now, true)) // '14:30:25.123 15/01/2024' (with milliseconds)

// Padding utility
console.log(padZeroStart(5, 3)) // '005'
console.log(padZeroStart(123, 2)) // '123'
```

### Number Formatting

```typescript
import { num } from '@kdt310722/utils'
// or
import { format, formatBytes, formatPercent, formatNanoseconds, BigIntMath } from '@kdt310722/utils/number'

// Number formatting
console.log(format(1234.5678)) // '1,234.5679' (default locale)
console.log(format(1234.5678, { maximumFractionDigits: 2 })) // '1,234.57'
console.log(humanizeNumber(1234567)) // '1.23M'

// Currency formatting
console.log(formatUsdCurrency(1234.56)) // '$1,234.56'

// Bytes formatting
console.log(formatBytes(1024 * 1024)) // '1.00MB'

// Percentage formatting
console.log(formatPercent(0.1234)) // '12.34%'

// Nanoseconds formatting
console.log(formatNanoseconds(1_500_000_000n)) // '1s 500ms'

// BigInt math operations
console.log(BigIntMath.abs(-5n)) // 5n
console.log(BigIntMath.max(3n, 7n)) // 7n
console.log(BigIntMath.min(3n, 7n)) // 3n
console.log(BigIntMath.pow(2n, 3n)) // 8n
console.log(BigIntMath.sign(-5n)) // -1n

// Number validation and conversion
console.log(isNumber(123)) // true
console.log(isBigInt(123n)) // true
console.log(isNumberString('123.45')) // true
console.log(isNumberish('123')) // true

console.log(toNumber('123.45')) // 123.45
console.log(toBigInt('123')) // 123n

// Math utilities
console.log(random(1, 10)) // Random number between 1 and 10
console.log(minMax(15, 1, 10)) // 10 (clamped to max)
console.log(minMaxBigInt(15n, 1n, 10n)) // 10n
```

### Function Utilities

```typescript
import { func } from '@kdt310722/utils'
// or
import { once, pipe, tap, transform, tryCatch } from '@kdt310722/utils/function'

// Higher-order functions
const expensiveFunction = (x: number) => {
    console.log('Computing...', x)
    return x * x
}

const memoized = once(expensiveFunction)
console.log(memoized(5)) // Computing... 5, returns 25
console.log(memoized(5)) // returns 25 (cached, no console.log)

// Function composition
const result = pipe(
    10,
    x => x * 2,    // 20
    x => x + 5,    // 25
    x => x / 5     // 5
)

// Utility functions
const value = tap(42, (x) => console.log('Processing:', x)) // Logs and returns 42
const transformed = transform('hello', (s) => s.toUpperCase()) // 'HELLO'

// Error handling
const safeResult = tryCatch(
    () => JSON.parse('invalid json'),
    'default value'
) // Returns 'default value'

const asyncSafeResult = await tryCatchAsync(
    () => fetch('/api/data').then(r => r.json()),
    { error: 'Failed to fetch' }
)
```

### Common Utilities

```typescript
import { common } from '@kdt310722/utils'
// or
import { typeOf, isEmpty, isTrueLike, isDeepEqual, assert } from '@kdt310722/utils/common'

// Type checking
console.log(typeOf([])) // 'array'
console.log(typeOf({})) // 'object'
console.log(typeOf(null)) // 'null'
console.log(typeOf(new Date())) // 'date'

// Empty checking
console.log(isEmpty('')) // true
console.log(isEmpty([])) // true
console.log(isEmpty({})) // true
console.log(isEmpty(0)) // true
console.log(isEmpty(false)) // true

// Truth-like checking
console.log(isTrueLike('true')) // true
console.log(isTrueLike('yes')) // true
console.log(isTrueLike('1')) // true
console.log(isTrueLike(1)) // true

// Deep equality
const obj1 = { a: { b: [1, 2, 3] } }
const obj2 = { a: { b: [1, 2, 3] } }
console.log(isDeepEqual(obj1, obj2)) // true

// Assertions
assert(typeof value === 'string', 'Value must be a string')
assert(array.length > 0, new Error('Array cannot be empty'))
```

### Node.js Utilities

```typescript
import { node } from '@kdt310722/utils'
// or
import { isInDevelopment, isInProduction, fetch, dirname } from '@kdt310722/utils/node'

// Environment detection
if (isInDevelopment()) {
    console.log('Running in development mode')
}

if (isInProduction()) {
    console.log('Running in production mode')
}

// Enhanced fetch with timeout and retry
const response = await fetch('https://api.example.com/data', {
    method: 'GET',
    headers: { 'Authorization': 'Bearer token' }
}, {
    timeout: 5000,
    retry: { enabled: true, retries: 3 }
})

// Path utilities
const currentDir = dirname(import.meta, 'config', 'settings.json')
```
## Best Practices

### Import Patterns

**Prefer Named Imports**
```typescript
// ✅ Good - specific imports
import { chunk, unique, shuffle } from '@kdt310722/utils/array'

// ✅ Good - namespace imports for related functionality
import { arr, obj, str } from '@kdt310722/utils'

// ❌ Avoid - importing everything
import * as utils from '@kdt310722/utils'
```

**Module-Specific Imports**
```typescript
// ✅ Good - when using many functions from same module
import {
    pick, omit, flatten, isEmpty,
    isPlainObject, LruMap
} from '@kdt310722/utils/object'

// ✅ Also good - for single functions
import { withRetry } from '@kdt310722/utils/promise'
```

### Type Safety

**Use Type Guards**
```typescript
import { isString, isNumber } from '@kdt310722/utils/string'
import { isPlainObject } from '@kdt310722/utils/object'
import { isDate } from '@kdt310722/utils/time'

function processValue(value: unknown) {
    if (isString(value)) {
        // TypeScript knows value is string here
        return value.toUpperCase()
    }

    if (isNumber(value)) {
        // TypeScript knows value is number here
        return value * 2
    }

    if (isPlainObject(value)) {
        // TypeScript knows value is Record<string, unknown>
        return Object.keys(value).length
    }

    if (isDate(value)) {
        // TypeScript knows value is Date here
        return value.getTime()
    }
}
```

**Generic Type Preservation**
```typescript
import { parseJson } from '@kdt310722/utils/json'
import { createDeferred } from '@kdt310722/utils/promise'

interface User {
    id: number
    name: string
    email: string
}

// ✅ Type-safe JSON parsing
const userData = parseJson<User>('{"id":1,"name":"Alice","email":"<EMAIL>"}')
// userData is User

// ✅ Type-safe deferred promises
const userDeferred = createDeferred<User>()
// userDeferred.promise is Promise<User>
```

### Performance Optimization

**Use LRU Collections for Caching**
```typescript
import { LruMap, LruSet } from '@kdt310722/utils/object'

// ✅ Good - bounded cache size
const userCache = new LruMap<string, User>(1000)
const processedIds = new LruSet<string>(500)

function getUser(id: string): Promise<User> {
    // Check cache first
    const cached = userCache.get(id)
    if (cached) {
        return Promise.resolve(cached)
    }

    // Fetch and cache
    return fetchUser(id).then((user) => {
        userCache.set(id, user)
        return user
    })
}
```

**Efficient Array Processing**
```typescript
import { chunk, mapAsync } from '@kdt310722/utils/array'

// ✅ Good - process large arrays in chunks
async function processLargeDataset(items: DataItem[]) {
    for (const chunkBatch of chunk(items, 100)) {
        await processBatch(chunkBatch)
    }
}

// ✅ Good - async array operations
const results = await mapAsync(items, async (item) => await processItem(item))
```

### Error Handling

**Use Custom Error Classes**
```typescript
import { BaseError } from '@kdt310722/utils/error'

class UserNotFoundError extends BaseError {
    public constructor(userId: string) {
        super(`User not found: ${userId}`, {
            code: 'USER_NOT_FOUND',
            retryable: false
        })
        this.withValue('userId', userId)
    }
}

class ValidationError extends BaseError {
    public constructor(field: string, value: unknown, reason: string) {
        super(`Validation failed for ${field}: ${reason}`, {
            code: 'VALIDATION_ERROR',
            retryable: false
        })
        this.withValue('field', field)
        this.withValue('value', value)
        this.withValue('reason', reason)
    }
}

// ✅ Good - specific error handling
try {
    const user = await findUser(id)
    if (!user) {
        throw new UserNotFoundError(id)
    }
} catch (error) {
    if (error instanceof UserNotFoundError) {
        // Handle specific error type
        logger.warn('User lookup failed', { userId: error.userId })
    } else {
        // Handle other errors
        logger.error('Unexpected error', { error })
    }
}
```

**Error Context and Chaining**
```typescript
import { BaseError } from '@kdt310722/utils/error'

async function saveUser(user: User) {
    try {
        await database.save(user)
    } catch (error) {
        // ✅ Good - preserve original error with context
        throw new BaseError('Failed to save user', { cause: error })
            .withValue('userId', user.id)
            .withValue('operation', 'save')
            .withValue('timestamp', new Date())
    }
}
```

### Async Patterns

**Use Deferred for Complex Async Logic**
```typescript
import { createDeferred } from '@kdt310722/utils/promise'

class AsyncResource {
    protected readonly readyDeferred = createDeferred<void>()

    public constructor() {
        this.initialize()
    }

    protected async initialize() {
        try {
            await this.setup()
            this.readyDeferred.resolve()
        } catch (error) {
            this.readyDeferred.reject(error)
        }
    }

    public async waitUntilReady() {
        return this.readyDeferred.promise
    }
}
```

**Implement Retry Logic**
```typescript
import { withRetry } from '@kdt310722/utils/promise'

// ✅ Good - configurable retry
async function fetchWithRetry(url: string) {
    return withRetry(
        () => fetch(url).then((r) => r.json()),
        {
            retries: 3,
            delay: 1000,
            onFailedAttempt: (error) => {
                logger.warn('Request failed, retrying', {
                    url,
                    attempt: error.attemptNumber,
                    error: error.message
                })
            }
        }
    )
}
```

**Use Locks for Resource Protection**
```typescript
import { createLock } from '@kdt310722/utils/promise'

class ResourceManager {
    protected readonly lock = createLock()
    protected resource: Resource | null = null

    public async getResource(): Promise<Resource> {
        return this.lock.run(async () => {
            if (!this.resource) {
                this.resource = await this.createResource()
            }
            return this.resource
        })
    }
}
```

### Object Manipulation

**Safe Object Operations**
```typescript
import { pick, omit, flatten, isEmpty } from '@kdt310722/utils/object'

// ✅ Good - sanitize objects for API responses
function sanitizeUser(user: User): PublicUser {
    return omit(user, 'password', 'secretKey', 'internalId')
}

// ✅ Good - extract specific fields
function createUserSummary(user: User): UserSummary {
    return pick(user, 'id', 'name', 'email', 'lastLogin')
}

// ✅ Good - handle nested configurations
function processConfig(config: NestedConfig) {
    const flattened = flatten(config)

    // Process flattened keys
    for (const [key, value] of Object.entries(flattened)) {
        if (key.endsWith('.enabled') && value === true) {
            enableFeature(key.replace('.enabled', ''))
        }
    }
}
```

### String Processing

**Consistent String Operations**
```typescript
import { ensurePrefix, stripSuffix, escapeRegExp } from '@kdt310722/utils/string'

// ✅ Good - consistent URL path handling
function buildApiPath(endpoint: string): string {
    const cleanEndpoint = stripSuffix(endpoint, '/')
    return ensurePrefix(cleanEndpoint, '/api/v1/')
}

// ✅ Good - safe user input processing
function createUserSlug(name: string): string {
    return name.toLowerCase().replace(/[^a-z0-9]+/g, '-')
}

// ✅ Good - safe regex construction from user input
function createSearchRegex(userInput: string): RegExp {
    const escaped = escapeRegExp(userInput)
    return new RegExp(escaped, 'i')
}
```

### Event-Driven Architecture

**Type-Safe Event Systems**
```typescript
import { Emitter } from '@kdt310722/utils/event'

// ✅ Good - define event interfaces
interface UserEvents {
    'created': (user: User) => void
    'updated': (user: User, changes: Partial<User>) => void
    'deleted': (userId: string) => void
    'error': (error: Error, context: string) => void
}

class UserService {
    protected readonly events = new Emitter<UserEvents>()

    public on<K extends keyof UserEvents>(
        event: K,
        listener: UserEvents[K]
    ) {
        return this.events.on(event, listener)
    }

    public async createUser(data: CreateUserData): Promise<User> {
        try {
            const user = await this.repository.create(data)
            this.events.emit('created', user)
            return user
        } catch (error) {
            this.events.emit('error', error, 'createUser')
            throw error
        }
    }
}
```

### Memory Management

**Proper Cleanup Patterns**
```typescript
import { Emitter } from '@kdt310722/utils/event'

class ResourceManager {
    protected readonly events = new Emitter()
    protected readonly resources = new Map<string, Resource>()

    public constructor() {
        // ✅ Good - cleanup on process exit
        process.on('SIGTERM', () => this.cleanup())
        process.on('SIGINT', () => this.cleanup())
    }

    protected async cleanup() {
        // Clean up resources
        for (const resource of this.resources.values()) {
            await resource.destroy()
        }
        this.resources.clear()

        // Remove all event listeners
        this.events.removeAllListeners()
    }
}
```

### Common Anti-Patterns to Avoid

**❌ Don't ignore type safety**
```typescript
// ❌ Bad
const result = parseJson(jsonString) as User

// ✅ Good
const result = parseJson<User>(jsonString)
// result is properly typed as User
```

**❌ Don't mix async patterns**
```typescript
// ❌ Bad - mixing callbacks with promises
function badAsyncPattern(callback: (error: Error | null, result?: string) => void) {
    withRetry(asyncOperation)
        .then(result => callback(null, result))
        .catch(error => callback(error))
}

// ✅ Good - consistent promise-based API
async function goodAsyncPattern(): Promise<string> {
    return withRetry(asyncOperation)
}
```

**❌ Don't ignore error context**
```typescript
// ❌ Bad - losing error context
try {
    await operation()
} catch (error) {
    throw new Error('Operation failed')
}

// ✅ Good - preserve error context
try {
    await operation()
} catch (error) {
    throw new BaseError('Operation failed', { cause: error })
        .withValue('context', additionalContext)
}
```
## Dependencies

This library has minimal external dependencies to maintain a small footprint:

- **json5**: Enhanced JSON parsing with support for comments and trailing commas
- **p-retry**: Promise-based retry utility with configurable strategies

All dependencies are carefully chosen for their reliability and focused functionality. The library is designed to work well in both Node.js and browser environments where supported.

### Key Features by Module

- **Array**: Generator-based chunking, async iterators, LRU Set, math operations
- **Buffer**: Buffer/ArrayBuffer/string conversion utilities
- **Common**: Type guards, assertions, deep equality, utility types
- **Error**: Enhanced error classes with context, cause chains, and serialization
- **Event**: Type-safe event emitter with full listener management
- **Function**: Higher-order functions, composition, error handling utilities
- **JSON**: JSON5 support with built-in BigInt and Error serialization
- **Node**: Environment detection, enhanced fetch, file system utilities
- **Number**: Advanced formatting, BigInt math, number validation
- **Object**: Path operations, flattening, LRU Map, type-safe manipulation
- **Promise**: Deferred promises, locks, retry logic, timeout handling
- **String**: Comprehensive string manipulation, hex utilities, URL validation
- **Time**: Date utilities, timestamp conversion, formatting

---

## Changelog

**Updated based on source code analysis:**

- ✅ **Corrected API signatures** - All function signatures now match the actual implementation
- ✅ **Updated import patterns** - Fixed namespace imports (`func` instead of `function`, etc.)
- ✅ **Added missing functions** - Included all functions found in source code
- ✅ **Removed non-existent functions** - Removed functions that don't exist in the library
- ✅ **Updated type definitions** - All types now match the actual TypeScript definitions
- ✅ **Enhanced examples** - All examples now use correct API calls
- ✅ **Corrected dependencies** - Updated to reflect actual dependencies (json5, p-retry)
- ✅ **Added comprehensive coverage** - Every module now has complete documentation

**Key corrections made:**

- Fixed `BaseError` constructor and methods
- Corrected `Emitter` event type definitions
- Updated `Promise` module functions (`createDeferred`, `withRetry`, etc.)
- Fixed `Object` module function signatures (`pick`, `omit`, `flatten`)
- Corrected `String` module function names and signatures
- Updated `Number` module formatting functions
- Fixed import paths and namespace names
