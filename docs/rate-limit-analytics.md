# Rate Limiting Analytics & Implementation Guide

## Context Overview

Tài liệu này tổng hợp toàn bộ phân tích về các thuật toán rate limiting được sử dụng bởi các endpoint/API providers và cách triển khai chúng trong dự án New RPC Proxy sử dụng thư viện Bottleneck.

**Mục tiêu**: Cung cấp context đầy đủ cho AI Coding Assistants để hiểu về rate limiting mà không cần phân tích lại.

## Project Context

**Dự án**: New RPC Proxy - High-performance JSON-RPC proxy server cho Solana Blockchain
**Vấn đề**: Các endpoint có rate limiting khác nhau, cần hiểu thuật toán để implement proxy hiệu quả
**Thư viện sử dụng**: Bottleneck (v2.19.5) - Zero-dependency rate limiting library

## Rate Limiting Algorithms Analysis

### 1. Token Bucket Algorithm
**Đặc điểm**:
- <PERSON><PERSON> "bucket" chứa tokens, mỗi request tiêu thụ 1 token
- Cho phép burst traffic trong giới hạn bucket capacity
- Refill tokens theo interval cố định

**Ứng dụng**: AWS API Gateway, Google Cloud APIs
**Ưu điểm**: Linh hoạt, cho phép traffic đột biến
**Use case**: Khi cần gửi ngay lập tức nếu có token available

### 2. Leaky Bucket Algorithm
**Đặc điểm**:
- Request được xử lý với tốc độ cố định
- Smooth traffic, không cho phép burst
- Như nước chảy qua lỗ thủng

**Ứng dụng**: Network traffic shaping
**Ưu điểm**: Traffic đều đặn, dễ dự đoán

### 3. Fixed Window Counter
**Đặc điểm**:
- Đếm số request trong khung thời gian cố định
- Reset counter khi hết window
- Ví dụ: 150 requests/phút, reset hoàn toàn sang phút mới

**Vấn đề**: Thundering herd ở biên giới window
**Use case**: Đơn giản, dễ implement

### 4. Sliding Window Log
**Đặc điểm**:
- Lưu timestamp của mỗi request
- Kiểm tra trong sliding window
- Chính xác nhất nhưng tốn memory

### 5. Sliding Window Counter
**Đặc điểm**:
- Kết hợp Fixed Window và Sliding Window
- Ước tính số request trong sliding window
- Cân bằng giữa độ chính xác và hiệu suất

### 6. Quota-based Systems
**Đặc điểm**:
- Credit pool system (VD: 1000 credit/tháng)
- Mỗi method có cost khác nhau
- Weight-based consumption

**Ví dụ**:
- `getBalance`: 1 credit
- `getTransaction`: 2 credits
- `sendTransaction`: 5 credits

### 7. Distributed Rate Limiting
**Đặc điểm**:
- Shared state across multiple servers
- Sử dụng Redis cho consistency
- Token Bucket/Sliding Window với Redis

### 8. Hierarchical Rate Limiting
**Đặc điểm**:
- Multi-tier: Per-second, per-minute, per-hour limits
- User-based: Different limits cho different user tiers
- Resource-based: Different limits cho different endpoints

## Real-world Rate Limiting Scenarios

### Scenario 1: Fixed Window Reset
```
Mỗi phút có 150 requests
Ở 22:50 còn 30 requests → 22:51 có ngay 150 requests
Algorithm: Fixed Window Counter
```

### Scenario 2: Credit + Method Based
```
1000 credit/tháng, mỗi method có cost khác nhau
Algorithm: Quota-based System với Weighted Rate Limiting
```

### Scenario 3: RPS/TPS (Solana RPC)
```
Giới hạn requests per second và transactions per second
Algorithm: Token Bucket hoặc Leaky Bucket
Thực tế: Multi-tier Rate Limiting
```

### Scenario 4: Simple Rate Limiting
```
Yêu cầu: 5 requests/giây, gửi ngay lập tức nếu có slot
Algorithm: Token Bucket (phù hợp nhất)
Cấu hình: capacity=5, refillRate=5/second
```

## Bottleneck Library Implementation

### Supported Algorithms

#### 1. Token Bucket
```javascript
const limiter = new Bottleneck({
  reservoir: 5,                    // Bucket capacity
  reservoirRefreshAmount: 5,       // Refill amount
  reservoirRefreshInterval: 1000,  // Refill interval (1s)
  maxConcurrent: 5,
  minTime: 0
});
```

#### 2. Fixed Window Counter
```javascript
const limiter = new Bottleneck({
  reservoir: 150,                  // 150 requests per window
  reservoirRefreshAmount: 150,     // Reset to 150
  reservoirRefreshInterval: 60000, // Every 1 minute
  maxConcurrent: null
});
```

#### 3. Leaky Bucket
```javascript
const limiter = new Bottleneck({
  maxConcurrent: 1,               // Process 1 at a time
  minTime: 200                    // 200ms between requests (5 RPS)
});
```

#### 4. Quota-based System
```javascript
const limiter = new Bottleneck({
  reservoir: 1000,                     // 1000 credits
  reservoirRefreshAmount: 1000,        // Reset monthly
  reservoirRefreshInterval: 30 * 24 * 60 * 60 * 1000
});

// Weighted requests
limiter.schedule({ weight: 1 }, () => getBalance());     // 1 credit
limiter.schedule({ weight: 2 }, () => getTransaction()); // 2 credits
limiter.schedule({ weight: 5 }, () => sendTransaction());// 5 credits
```

#### 5. Distributed Rate Limiting
```javascript
const limiter = new Bottleneck({
  id: "shared-endpoint-limiter",
  datastore: "redis",
  clientOptions: { host: "redis-server" },
  maxConcurrent: 10,
  minTime: 100
});
```

### Multi-Algorithm Combinations

#### Method 1: Chain Multiple Limiters
```javascript
// Per-second limiter (Token Bucket)
const perSecondLimiter = new Bottleneck({
  maxConcurrent: 5,
  minTime: 200  // 5 RPS
});

// Per-hour limiter (Fixed Window)
const perHourLimiter = new Bottleneck({
  reservoir: 1000,
  reservoirRefreshAmount: 1000,
  reservoirRefreshInterval: 60 * 60 * 1000
});

// Chain them together
perSecondLimiter.chain(perHourLimiter);
```

#### Method 2: Group-based Multi-tier
```javascript
const endpointGroup = new Bottleneck.Group({
  maxConcurrent: 1,
  minTime: 1000
});

const endpoint1Limiter = endpointGroup.key("endpoint1");
const endpoint2Limiter = endpointGroup.key("endpoint2");
```

#### Method 3: Hierarchical Limiting
```javascript
// Global limiter
const globalLimiter = new Bottleneck({
  maxConcurrent: 50,
  reservoir: 10000,
  reservoirRefreshInterval: 60000
});

// Per-user limiters
const userGroup = new Bottleneck.Group({
  maxConcurrent: 5,
  minTime: 100
});

// Chain user limiters to global
userGroup.on("created", (limiter, key) => {
  limiter.chain(globalLimiter);
});
```

## Rate Limit Event Detection

### 1. Depleted Event (Reservoir = 0)
```javascript
limiter.on("depleted", (empty) => {
  console.log("🚨 Rate limit reached! Reservoir depleted");
  eventEmitter.emit("rateLimitReached", {
    endpoint: "api.example.com",
    timestamp: Date.now(),
    type: "reservoir_depleted"
  });
});
```

### 2. Dropped Event (Queue overflow)
```javascript
limiter.on("dropped", (dropped) => {
  console.log("🚨 Request dropped due to rate limit");
  eventEmitter.emit("rateLimitReached", {
    endpoint: "api.example.com",
    droppedJob: dropped,
    reason: "queue_overflow"
  });
});
```

### 3. Custom Rate Limit Detection
```javascript
limiter.on("executing", async (jobInfo) => {
  const reservoir = await limiter.currentReservoir();
  const running = await limiter.running();
  
  if (reservoir <= 5) { // Threshold warning
    eventEmitter.emit("rateLimitWarning", {
      endpoint: "api.example.com",
      remainingRequests: reservoir,
      runningJobs: running
    });
  }
});
```

### 4. Health Check Monitoring
```javascript
setInterval(async () => {
  const counts = limiter.counts();
  const reservoir = await limiter.currentReservoir();
  
  if (counts.QUEUED > 100 || reservoir === 0) {
    eventEmitter.emit("rateLimitReached", {
      endpoint: "api.example.com",
      queuedJobs: counts.QUEUED,
      reservoir: reservoir,
      timestamp: Date.now()
    });
  }
}, 1000);
```

## Algorithm Mapping Summary

| Scenario | Algorithm | Bottleneck Config |
|----------|-----------|-------------------|
| Reset mỗi phút | Fixed Window Counter | `reservoir` + `reservoirRefreshInterval` |
| Credit + Method | Quota-based System | `reservoir` + `weight` |
| RPS/TPS | Token Bucket | `reservoir` + `reservoirRefresh*` |
| Simple 5 req/sec | Token Bucket | `capacity=5, refillRate=5/sec` |
| Smooth traffic | Leaky Bucket | `maxConcurrent` + `minTime` |
| Distributed | Any + Redis | `datastore: "redis"` |
| Multi-tier | Chain/Group | `chain()` method |

## Bottleneck Capabilities Matrix

| Feature | Support | Implementation |
|---------|---------|----------------|
| Token Bucket | ✅ | `reservoir` + `reservoirRefresh*` |
| Fixed Window | ✅ | `reservoir` + `reservoirRefreshInterval` |
| Leaky Bucket | ✅ | `maxConcurrent` + `minTime` |
| Quota System | ✅ | `reservoir` + `weight` |
| Distributed | ✅ | `datastore: "redis"` |
| Multi-tier | ✅ | `chain()` method |
| Rate Limit Events | ✅ | `depleted`, `dropped`, custom monitoring |
| Weighted Requests | ✅ | `weight` option in job |
| Priority Queues | ✅ | `priority` option (0-9) |
| Burst Support | ✅ | Token Bucket configuration |
| Clustering | ✅ | Redis datastore |

## Key Insights for Implementation

1. **Token Bucket** là thuật toán linh hoạt nhất, phù hợp cho hầu hết use cases
2. **Bottleneck** có thể triển khai hầu hết các thuật toán rate limiting phổ biến
3. **Chain mechanism** cho phép kết hợp nhiều thuật toán hiệu quả
4. **Event system** mạnh mẽ để detect và handle rate limit scenarios
5. **Redis support** cho distributed rate limiting across multiple instances
6. **Weight system** hỗ trợ quota-based và method-specific limiting

## Recommendations

- Sử dụng **Token Bucket** cho general purpose rate limiting
- Sử dụng **Chain** để implement multi-tier limiting
- Sử dụng **Group** cho per-endpoint/per-user limiting
- Luôn listen **depleted** và **dropped** events
- Implement **health check monitoring** cho proactive detection
- Sử dụng **Redis datastore** cho distributed environments

---

*Tài liệu này cung cấp context đầy đủ về rate limiting analysis và implementation với Bottleneck library cho New RPC Proxy project.*
